const HOST = `${import.meta.env.VITE_MAZE_HOST}/web/v1`

export default {
  register_device: `${HOST}/mirror/register-device`, // post { device_id, direction, merchant_no }
  get_tokens: `${HOST}/mirror/get-tokens`, // get virtual_uid
  get_virtual_device: `${HOST}/mirror/get-virtual-device`,
  // detail
  send_image_email: `${HOST}/mirror/send-batch-image-email`, // post { device_id, image, merchant_no }
  send_image_phone: `${HOST}/mirror/send-image-phone`, // post { device_id, image, merchant_no }
  store_image: `${HOST}/mirror/store-batch-image`, // post { device_id, image, merchant_no }
  thumb_image_mirror: `${HOST}/mirror/thumb-image`, // post { device_id, image, merchant_no }
  // 扣分
  draw_mirror: `${HOST}/draw-mirror`, // post { number }  已废弃，后端处理扣分
  // event
  event_list: `${HOST}/event/new/list`,
  event_detail: `${HOST}/event/new/detail`,
  get_image: `${HOST}/mirror/get-batch-image`, // post {material_ids}
  // res pack
  // theme
  themes: `${HOST}/mirror-themes`, // 获取资源包 废弃，使用
  theme_detail: `${HOST}/mirror-themes/detail`, // 获取主题详情
  // draw 整合后的绘画接口
  draw: `${HOST}/mirror/draw`,
  // record
  record_download: `${HOST}/mirror/record-download`, // post { event_id }
  record_print: `${HOST}/mirror/record-print`, // post { event_id }
  // pay
  create_image_payment_order: `${HOST}/mirror/create-image-payment-order`, // post
  get_image_payment_order: `${HOST}/mirror/get-image-payment-order`, // post { order_id }
  create_virtual_device_token: `${HOST}/mirror/create-device-token-tmp`, // get virtual_uid
  get_tokens_tmp: `${HOST}/mirror/get-device-token-tmp`,
  // print via machine
  print_notify: `${HOST}/mirror/print/notify`, // 提交打印状态 post, { event_id, fail_reason, status, task_id}
  print_submit: `${HOST}/mirror/print/submit`, // 创建打印任务 post, { device_id, event_id, material_id }
  print_status: `${HOST}/mirror/print/status`, // 获取打印任务状态 get, { task_id }
  print_task: `${HOST}/mirror/print/task`, // 获取打印任务列表 get, { device_id }
}
