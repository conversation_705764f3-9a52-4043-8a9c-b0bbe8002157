import classnames from 'classnames'
import { use<PERSON><PERSON>, useAtomValue } from 'jotai'
import { PrinterStatus } from '@/stores/types'
import { VirtualInfo, ThemeDetail } from '@/apis/types'
import confetti, { CreateTypes } from 'canvas-confetti'
import useSwr from 'swr'
import styles from './Detail.module.css'
import {
  resultOrderAtom,
  printerEnableAtom,
  machineInfoAtom,
  selectedEventIdAtom,
  selectedImageFrameAtom,
  selectedEventDetailAtom,
  screenOrientationAtom,
  selectedThemeDetailAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { useEffect, useState, useMemo, useRef } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { useDevice } from '@/hooks/useDevice'
import { useTranslation } from 'react-i18next'
import { SendToEmailModal } from './SendToEmailModal'
import { DownloadModal } from './DownloadModal'
import { ShareModal } from './ShareModal'
import { LikeOrNot } from './LikeOrNot'
import { PrintModal } from './PrintModal'
import { downloadRemoteFile, isIphone, isPad, isWebApp } from '@/utils'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import { MyMirrorAiTask } from '@/stores/types'
import { AiTaskDetailType } from '@/graphqls/types'

import { publicPreLoadSourceObj } from '@/configs/source'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import classNames from 'classnames'
import { isIPad } from '@/utils'
import { MirrorLoading } from 'wujieai-react-icon'
import { useBridge } from '@/hooks/useBridge'
import { toast } from '@/components/ui/shad/use-toast'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { ScanQrPrintModal } from './ScanQrPrintModal'

export const ImageDetail = () => {
  const [curMaterialId, setCurMaterialId] = useState<
    { material_id: number; image_id: number }[] | null
  >(null)
  const [curVirtualInfo, setCurVirtualInfo] = useState<VirtualInfo>()
  // Flag to track if storeImageToMaze has been called for the current set of images
  const hasStoredImages = useRef(false)
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const selectedThemeDetail = useAtomValue(selectedThemeDetailAtom)
  const confettiRef = useRef<CreateTypes>()
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [selectedFrame] = useAtom(selectedImageFrameAtom)
  const { getDefaultDeviceInfo } = useDevice()
  const { downImage } = useBridge()
  const { t } = useTranslation()

  const [curOptType, setCurOptType] = useState<string | null>(null)
  const [isLikeBtnShow, setIsLikeBtnShow] = useState(true)
  // Current active image index in the slide show
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  // Store all selected images with their taskBaseIds
  const [selectedImageData, setSelectedImageData] = useState<{
    taskBaseIds: number[]
    imageIds: { [taskBaseId: number]: number[] }
    images: MyMirrorAiTask[]
  }>({
    taskBaseIds: [],
    imageIds: {},
    images: [],
  })

  const [searchParams] = useSearchParams()
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  // 获取图片边框合成结果，轮询
  const { data: materiaDetail } = useSwr(
    curMaterialId ? [curMaterialId] : null,
    ([material_ids]) =>
      _ajax.post(_api.get_image, {
        material_ids: material_ids?.map(it => it.material_id),
      }),
    {
      refreshInterval: data => {
        const items = data?.data?.data
        const isAllImageCompleted =
          Array.isArray(items) &&
          items.length > 0 &&
          items.every((it: any) => it.url)
        // console.log(
        //   'refresh',
        //   isAllImageCompleted,
        //   items,
        //   items?.every((it: any) => it.url)
        // )
        if (isAllImageCompleted) {
          return 0 // Stop polling when all images are completed
        } else {
          return 3_000
        }
      },
    }
  )
  // Current active image
  const curImg = useMemo(() => {
    return selectedImageData.images[activeImageIndex] || null
  }, [selectedImageData.images, activeImageIndex])
  const curVideo = curImg?.video

  const curMaterialDetail = useMemo(() => {
    return materiaDetail?.data?.data?.find(
      (it: any) => it.wj_image_id === curImg?.id
    )
  }, [materiaDetail, curImg])
  console.log('🚀 ~ ImageDetail ~ curMaterialDetail:', curMaterialDetail)
  const curMazeMaterialImageUrl = useMemo(() => {
    return curMaterialDetail?.url
  }, [curMaterialDetail])

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])

  const toolbarList = useMemo(() => {
    return isWebApp()
      ? [
          {
            icon: publicPreLoadSourceObj.detailDownload,
            title: t('下载'),
            op: 'download',
            disabled: !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_download,
          },
          {
            icon: publicPreLoadSourceObj.detailEmail,
            title: t('邮件'),
            op: 'email',
            disabled: curMaterialId === null,
            enable: selectedEventDetail?.enable_mail,
          },
          {
            icon: publicPreLoadSourceObj.detailPrint,
            title: t('打印'),
            op: 'scanQrPrint',
            // disabled: !hasPrinter || !curMazeMaterialImageUrl,
            // enable: selectedEventDetail?.enable_print,
          },
          {
            icon: publicPreLoadSourceObj.detailShare,
            title: t('分享'),
            op: 'share',
            disabled: isWebApp() ? false : true,
            enable: true,
          },
        ]
      : [
          {
            icon: publicPreLoadSourceObj.detailPrint,
            title: t('打印'),
            op: 'print',
            disabled: !hasPrinter || !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_print,
          },
          {
            icon: publicPreLoadSourceObj.detailEmail,
            title: t('邮件'),
            op: 'email',
            disabled: curMaterialId === null,
            enable: selectedEventDetail?.enable_mail,
          },
          {
            icon: publicPreLoadSourceObj.detailDownload,
            title: t('下载'),
            op: 'download',
            disabled: !curMazeMaterialImageUrl,
            enable: selectedEventDetail?.enable_download,
          },
          {
            icon: publicPreLoadSourceObj.detailShare,
            title: t('分享'),
            op: 'share',
            disabled: isWebApp() ? false : true,
            enable: true,
          },
        ].filter((it: any) => it.enable)
  }, [selectedEventDetail, hasPrinter, curMazeMaterialImageUrl, isWebApp])

  // Parse the images parameter from URL
  // Format: /detail?images=taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
  const imagesParam = searchParams.get('images')
  const imagesCount = useMemo(() => {
    if (!imagesParam) return 0
    // 计算所有图片的总数，而不是任务数量
    let totalCount = 0
    imagesParam.split(';').forEach(taskImagePair => {
      const [, imageIdsStr] = taskImagePair.split(':')
      if (imageIdsStr) {
        totalCount += imageIdsStr.split(',').length
      }
    })
    return totalCount
  }, [imagesParam])

  // Parse the images parameter and fetch the images
  useEffect(() => {
    if (!imagesParam) return

    try {
      // Parse the images parameter
      // Format: taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
      const taskImageMap: { [taskBaseId: number]: number[] } = {}
      const taskBaseIds: number[] = []

      imagesParam.split(';').forEach(taskImagePair => {
        const [taskId, imageIdsStr] = taskImagePair.split(':')
        const taskBaseId = Number(taskId)
        const imageIds = imageIdsStr.split(',').map(Number)

        taskImageMap[taskBaseId] = imageIds
        taskBaseIds.push(taskBaseId)
      })

      setSelectedImageData(prev => ({
        ...prev,
        taskBaseIds,
        imageIds: taskImageMap,
      }))

      // Stop any existing polling before starting new ones
      stopPollAiTaskStatus()

      // Create a single polling function that handles all taskBaseIds
      const pollAllTasks = () => {
        // Use Promise.all to fetch all tasks in parallel
        Promise.all(
          taskBaseIds.map(
            taskBaseId =>
              new Promise(resolve => {
                pollAiTaskStatus({
                  taskBaseId,
                  onProgress({ taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    resolve(null)
                  },
                  onSuccess({ order, taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    setResultOrder(order)
                    resolve(null)
                  },
                  onFail() {
                    console.error(
                      `Failed to fetch images for taskBaseId: ${taskBaseId}`
                    )
                    resolve(null)
                  },
                })
              })
          )
        ).catch(err => {
          console.error('Error polling tasks:', err)
        })
      }

      // Start polling
      pollAllTasks()
    } catch (error) {
      console.error('Error parsing images parameter:', error)
    }
  }, [imagesParam])

  // Update the images for a specific taskBaseId
  const updateImagesForTask = (
    taskBaseId: number,
    taskList: MyMirrorAiTask[]
  ) => {
    console.log('updateImagesForTask', taskBaseId, taskList)
    setSelectedImageData(prev => {
      // Filter the task list to only include DRAW type results
      const drawImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.DRAW
      )
      // Get video results for later use
      const videoImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.VIDEO
      )
      const resultImages = drawImages?.map(item => ({
        ...item,
        video: videoImages?.[0],
      }))

      return {
        ...prev,
        images: [...prev.images, ...resultImages],
      }
    })
  }

  console.log(
    'result',
    hasPrinter,
    printerEnable,
    machineInfo,
    curImg,
    selectedEventDetail,
    selectedImageData
  )

  // 动效
  useEffect(() => {
    if (curImg) {
      const canvas = document.getElementById('detail-canvas-container')
      if (!confettiRef.current) {
        confettiRef.current = confetti.create(canvas as HTMLCanvasElement, {
          resize: true,
        })
      }
      const count = 600
      const fire = (particleRatio: number, opts: any) => {
        confettiRef.current?.({
          ...opts,
          origin: { y: 0.68, x: 0.5 },
          colors: ['#bb0000', '#71bab8', '#fd97b0', '#fcb73f'],
          particleCount: Math.floor(count * particleRatio),
          scalar: (opts?.scalar ? opts?.scalar : 0) * 1.5,
          startVelocity: 60,
          shapes: ['square'], // 'circle', 'star'
        })
      }
      fire(0.25, {
        spread: 26,
        startVelocity: 55,
      })
      fire(0.2, {
        spread: 60,
      })
      fire(0.35, {
        spread: 100,
        decay: 0.91,
        scalar: 0.8,
      })
      fire(0.1, {
        spread: 120,
        startVelocity: 25,
        decay: 0.92,
        scalar: 1.2,
      })
      fire(0.1, {
        spread: 120,
        startVelocity: 45,
      })
      fire(0.5, {
        spread: 360,
        startVelocity: 45,
      })
    }
  }, [curImg])

  const { pollAiTaskStatus, stopPollAiTaskStatus } = useAiTask()

  const handleToolbarClick = (op: string) => {
    if (op === 'download') {
      _ajax.post(_api.record_download, { event_id: selectedEventId })
      if (isWebApp()) {
        downloadRemoteFile(curVideo?.resultUrl || curMazeMaterialImageUrl)
        return
      }
      if (isIphone()) {
        downImage({
          imageUrls: [curMazeMaterialImageUrl],
        })
        toast({
          description: t('保存成功'),
        })
        return
      }
    }
    if (op === 'print') {
      _ajax.post(_api.record_print, { event_id: selectedEventId })
    }
    setCurOptType(op)
  }

  useEffect(() => {
    // 解决重复store问题\
    console.log(
      'storeImageToMaze',
      selectedImageData.images.length,
      imagesCount,
      selectedEventId,
      hasStoredImages.current
    )
    if (
      selectedImageData.images.length === imagesCount &&
      selectedEventId &&
      !hasStoredImages.current
    ) {
      storeImageToMaze(selectedImageData.images, selectedEventId, selectedFrame)
      // Set flag to true to prevent multiple calls
      hasStoredImages.current = true
    }
  }, [selectedImageData, selectedEventId, selectedFrame, imagesCount])

  useEffect(() => {
    getVirtualDevice()
  }, [])

  const getVirtualDevice = async () => {
    const deviceInfo = await getDefaultDeviceInfo()
    const res = await _ajax.post(_api.get_virtual_device, {
      device_id: deviceInfo?.id,
    })
    if (res.data?.code === 200) {
      setCurVirtualInfo(res.data.data)
    }
  }

  const storeImageToMaze = async (
    images: any,
    event_id: number,
    selectedFrame: any
  ) => {
    // Check if all images have resultUrl
    const allImagesHaveUrls = images.every((it: any) => it.resultUrl)

    if (!allImagesHaveUrls) {
      console.log(
        'Not all images have resultUrl, will store them later when processed'
      )
      // Don't set hasStoredImages to true yet, as we'll need to store them again when all have URLs
      return
    }

    // Track retry attempts to prevent infinite retries
    const maxRetries = 3
    let retryCount = 0
    let success = false

    while (!success && retryCount < maxRetries) {
      try {
        const res = await _ajax.post(_api.store_image, {
          event_id,
          theme_id: selectedThemeDetail?.id,
          ...(selectedFrame?.id ? { frame_id: selectedFrame.id } : {}),
          images: images.map((it: any) => {
            const isVideo = !!it?.video
            return {
              image_id: it.id,
              url: isVideo ? it.video.resultUrl : it.resultUrl,
              cover_image_url: isVideo ? it.resultUrl : null,
              type: isVideo ? 'video' : 'image',
            }
          }),
        })
        const materialId = res.data?.data?.images
        if (materialId) {
          setCurMaterialId(materialId)
          success = true
        } else {
          // If no material ID was returned but no error was thrown
          retryCount++
          console.warn(
            `No material ID returned, retry attempt ${retryCount}/${maxRetries}`
          )
          await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second before retrying
        }
      } catch (error) {
        retryCount++
        console.error(
          `Error storing images (attempt ${retryCount}/${maxRetries}):`,
          error
        )

        if (retryCount >= maxRetries) {
          console.error('Max retry attempts reached, giving up')
          // Only reset the flag after max retries to prevent excessive retries
          hasStoredImages.current = false
        } else {
          // Wait before retrying with exponential backoff
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1))
          )
        }
      }
    }
  }

  // Cleanup all intervals and timers when component unmounts
  useEffect(() => {
    return () => {
      // Stop polling for AI task status
      stopPollAiTaskStatus()

      // Clear any confetti animations
      if (confettiRef.current) {
        confettiRef.current.reset()
      }

      // Reset the stored images flag
      hasStoredImages.current = false
    }
  }, [])

  // Update images with processed URLs when materiaDetail changes
  useEffect(() => {
    if (materiaDetail?.data?.data && selectedImageData.images.length > 0) {
      // Define the type for processed images
      interface ProcessedImage {
        wj_image_id: number
        url: string
        id: number
        material_id: number
        cover_image_url: string
      }

      const processedImages = materiaDetail.data.data as ProcessedImage[]

      // Create a map of current image URLs to avoid unnecessary updates
      const currentImageUrls = new Map(
        selectedImageData.images.map(img => [img.id, img.resultUrl])
      )

      // Check if we need to update any images - only update if URLs are different
      const imagesToUpdate: { id: number; url: string }[] = []

      processedImages.forEach((processedImg: ProcessedImage) => {
        const currentUrl = currentImageUrls.get(processedImg.wj_image_id)
        if (
          processedImg.cover_image_url && // Only consider images with URLs
          processedImg.url && // Only consider images with URLs
          processedImg.wj_image_id && // Only consider images with valid IDs
          currentUrl !== processedImg.url // Only update if URL is different
        ) {
          imagesToUpdate.push({
            id: processedImg.wj_image_id,
            url: processedImg.url,
          })
        }
      })

      const needsUpdate = imagesToUpdate.length > 0

      if (needsUpdate) {
        // Use a ref to track if we're currently updating to prevent circular updates
        const updatingRef = { current: true }

        setSelectedImageData(prev => {
          // Create a new array with updated image URLs
          const updatedImages = prev.images.map(img => {
            // Find if this image needs updating
            const updateInfo = imagesToUpdate.find(
              update => update.id === img.id
            )

            // If found and has a URL, update the image's resultUrl
            if (updateInfo) {
              return {
                ...img,
                resultUrl: updateInfo.url,
              }
            }
            // Otherwise return the original image
            return img
          })

          // After update is complete
          setTimeout(() => {
            updatingRef.current = false
          }, 0)

          return {
            ...prev,
            images: updatedImages,
          }
        })
      }
    }
    // Only depend on materiaDetail to prevent circular updates
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [materiaDetail])

  // Convert selected images to format expected by MazeSingleTemplateList
  const imageTemplates = useMemo(() => {
    const baseTemplates = selectedImageData.images.map(img => ({
      id: img.id,
      cover_image: img.resultUrl || img.editResultUrls?.[2],
      cover_image_female: img.resultUrl || img.editResultUrls?.[2], // 为了组件复用
      video: (img as any).video || undefined, // 添加视频URL，与cover_image同级
      template_count: 0,
      name: '', // 添加缺少的属性
      type: 0 as 0 | 1, // 添加缺少的属性，0表示图片
      price: 0, // 添加缺少的属性
    })) as (ThemeDetail & { cover_video?: string | null })[]

    // 如果图片数量大于1且小于4，重复图片列表直到达到至少4张图片以支持loop模式
    if (baseTemplates.length > 1 && baseTemplates.length < 5) {
      const extendedTemplates = [...baseTemplates]
      while (extendedTemplates.length < 5) {
        // 重复添加原始图片列表中的图片，直到达到4张
        const remainingSlots = 5 - extendedTemplates.length
        const templatesToAdd = baseTemplates.slice(
          0,
          Math.min(remainingSlots, baseTemplates.length)
        )
        extendedTemplates.push(...templatesToAdd)
      }
      return extendedTemplates
    }

    return baseTemplates
  }, [selectedImageData.images])

  // Create a state setter function that matches the expected type
  const setActiveTemplate = useMemo(() => {
    return (
      value: React.SetStateAction<
        (ThemeDetail & { cover_video?: string | null }) | null | undefined
      >
    ) => {
      // If it's a function, call it with the current active template
      if (typeof value === 'function') {
        const currentTemplate = imageTemplates[activeImageIndex]
        const newTemplate = value(currentTemplate)
        if (!newTemplate) return

        // 找到原始图片列表中对应的索引
        const originalIndex = selectedImageData.images.findIndex(
          img => img.id === newTemplate.id
        )
        if (originalIndex !== -1) {
          setActiveImageIndex(originalIndex)
        }
      }
      // If it's a direct value
      else if (value) {
        // 找到原始图片列表中对应的索引
        const originalIndex = selectedImageData.images.findIndex(
          img => img.id === value.id
        )
        if (originalIndex !== -1) {
          setActiveImageIndex(originalIndex)
        }
      }
    }
  }, [imageTemplates, activeImageIndex, selectedImageData.images])

  return (
    <>
      <div className="flex items-center justify-center gap-20 h-full w-full flex-col pt-12">
        {selectedImageData.images.length > 0 ? (
          <>
            <div
              className={classNames(
                'h-[60vh] relative ',
                screenOrientation.isLandScape
                  ? 'w-[80vw] scale-[0.8]'
                  : 'w-full scale-100'
              )}
            >
              {/* Image Slide Show */}
              <MazeSingleTemplateList
                selectTemplateList={imageTemplates}
                activeTemplate={imageTemplates[activeImageIndex]}
                setActiveTemplate={setActiveTemplate}
                listKey="detail-image-list"
                multiline={false}
                swiperProps={{
                  slidesPerView: screenOrientation.isLandScape
                    ? 3
                    : isIPad()
                      ? 1.62
                      : isPad()
                        ? 1.72
                        : 1.32,
                  loop: true,
                  initialSlide: 0,
                }}
              />
              {/* Like Button */}
              {isLikeBtnShow &&
                selectedEventDetail?.enable_evaluation &&
                curImg && (
                  <div
                    className={classNames(
                      'absolute w-[50%] bottom-36 z-10 right-0 ',
                      screenOrientation.isLandScape
                        ? 'scale-75 bottom-[6%] right-[30%]'
                        : 'right-[22%] ipad:bottom-28 ipad:right-[22%] ipad:scale-75'
                    )}
                  >
                    <LikeOrNot
                      imgId={curMaterialDetail?.id}
                      after={() => setIsLikeBtnShow(false)}
                    />
                  </div>
                )}
            </div>

            <h2 className="maze-primary-text text-[2.5rem] text-center opacity-0 animate-text-fade-in phone:py-4">
              {t('分享你的AI写真')}
            </h2>

            {/* Toolbar */}
            <ul className={classnames([styles.toolbar])}>
              {toolbarList.map((it, i) => (
                <li
                  className={classnames([it.disabled ? 'opacity-50' : ''])}
                  key={i}
                  onClick={() => handleToolbarClick(it.disabled ? '' : it.op)}
                >
                  <SvgIcon
                    src={it.icon}
                    alt={it.title}
                    svgClassName="w-14 h-14"
                  />
                  <p className="text-[2rem] text-white text-center font-semibold absolute -left-[20%] -right-[20%] -bottom-[3.6rem]">
                    {it.title}
                  </p>
                </li>
              ))}
            </ul>
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="relative w-[6rem] h-[6rem] opacity-30">
              <MirrorLoading className="w-full h-full absolute left-0 top-0 animate-spin maze-primary-text" />
            </div>
          </div>
        )}

        <canvas
          className="fixed top-0 left-0 right-0 bottom-0 w-full h-full -z-10"
          id="detail-canvas-container"
        ></canvas>
      </div>
      <SendToEmailModal
        open={curOptType === 'email'}
        setOpen={() => setCurOptType(null)}
        imgId={curMaterialId}
      />
      <DownloadModal
        open={curOptType === 'download'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={
          curVirtualInfo?.status === 1 &&
          curVirtualInfo?.expire_ts * 1000 > +new Date()
            ? `${window.location.href}&virtual_uid=${curVirtualInfo?.uid}&navigate=noop`
            : curVideo
              ? curVideo.resultUrl
              : curMazeMaterialImageUrl
        }
      />
      <PrintModal
        open={curOptType === 'print'}
        setOpen={() => setCurOptType(null)}
        curImg={curImg}
        mazeImgUrl={curMazeMaterialImageUrl}
      />
      <ShareModal
        open={curOptType === 'share'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={curMazeMaterialImageUrl}
      />
      <ScanQrPrintModal
        open={curOptType === 'scanQrPrint'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={curMazeMaterialImageUrl}
        curMaterialId={curMaterialDetail?.id}
      />
    </>
  )
}
