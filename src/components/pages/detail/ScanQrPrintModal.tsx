import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom, selectedEventIdAtom } from '@/stores'
import { useAtomValue } from 'jotai'
import { Button } from '@/components/ui/shad/button'
import { Html5Qrcode } from 'html5-qrcode'
import { useEffect, useRef, useState } from 'react'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { PrintStatusEnum } from '@/apis/types'
import { publicPreLoadSourceObj } from '@/configs/source'
import { MirrorLoading } from 'wujieai-react-icon'

// 打印状态枚举
enum PrintModalStatus {
  INITIAL = 'initial', // 初始状态
  SCANNING = 'scanning', // 扫码中
  SUBMITTING = 'submitting', // 提交打印任务中
  PRINTING = 'printing', // 打印中
  SUCCESS = 'success', // 打印成功
  FAILED = 'failed', // 打印失败
}

export const ScanQrPrintModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  mazeImgUrl: string
  curMaterialDetail: any
}> = ({ open, setOpen, curMaterialDetail }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const selectedEventId = useAtomValue(selectedEventIdAtom)

  const [status, setStatus] = useState<PrintModalStatus>(
    PrintModalStatus.INITIAL
  )
  const [taskId, setTaskId] = useState<number | null>(null)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [isScanning, setIsScanning] = useState<boolean>(false)
  const scannerRef = useRef<Html5Qrcode | null>(null)
  const statusPollingRef = useRef<number | null>(null)

  // 解析二维码获取device_id
  const parseDeviceIdFromQrCode = (qrText: string): string | null => {
    try {
      // 假设二维码格式为 "device_id;其他信息" 或直接是device_id
      const parts = qrText.split(';')
      return parts[0] || null
    } catch (error) {
      console.error('解析二维码失败:', error)
      return null
    }
  }

  // 提交打印任务
  const submitPrintTask = async (deviceId: string) => {
    try {
      setStatus(PrintModalStatus.SUBMITTING)

      const response = await _ajax.post(_api.print_submit, {
        device_id: deviceId,
        event_id: curMaterialDetail?.event_id,
        material_id: curMaterialDetail?.id,
      })

      if (response.data?.code === 200 && response.data?.data?.task_id) {
        const newTaskId = response.data.data.task_id
        setTaskId(newTaskId)
        setStatus(PrintModalStatus.PRINTING)
        startStatusPolling(newTaskId)
      } else {
        setStatus(PrintModalStatus.FAILED)
        setErrorMessage(response.data?.msg || '提交打印任务失败')
      }
    } catch (error: any) {
      console.error('提交打印任务失败:', error)
      setStatus(PrintModalStatus.FAILED)
      setErrorMessage(error.message || '网络错误')
    }
  }

  // 开始轮询任务状态
  const startStatusPolling = (taskId: number) => {
    const pollStatus = async () => {
      try {
        const response = await _ajax.get(_api.print_status, {
          params: { task_id: taskId },
        })

        if (response.data?.code === 200) {
          const taskStatus = response.data.data?.status

          if (taskStatus === PrintStatusEnum.SUCCESS) {
            setStatus(PrintModalStatus.SUCCESS)
            stopStatusPolling()
          } else if (taskStatus === PrintStatusEnum.FAILED) {
            setStatus(PrintModalStatus.FAILED)
            setErrorMessage(response.data.data?.fail_reason || '打印失败')
            stopStatusPolling()
          }
          // 如果是PENDING或PRINTING状态，继续轮询
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
      }
    }

    // 立即执行一次
    pollStatus()

    // 每3秒轮询一次
    statusPollingRef.current = window.setInterval(pollStatus, 3000)
  }

  // 停止状态轮询
  const stopStatusPolling = () => {
    if (statusPollingRef.current) {
      clearInterval(statusPollingRef.current)
      statusPollingRef.current = null
    }
  }

  // 重置状态
  const resetStatus = async () => {
    setStatus(PrintModalStatus.INITIAL)
    setTaskId(null)
    setErrorMessage('')
    stopStatusPolling()

    // 停止扫码
    await stopScanning()
  }

  // 处理继续按钮点击
  const handleSubmit = () => {
    setStatus(PrintModalStatus.SCANNING)
  }

  // 开始扫码
  const startScanning = async () => {
    try {
      setIsScanning(true)

      // 创建Html5Qrcode实例
      const html5QrCode = new Html5Qrcode('qr-reader')
      scannerRef.current = html5QrCode

      // 获取摄像头权限并开始扫码
      await html5QrCode.start(
        { facingMode: 'environment' }, // 优先使用后置摄像头
        {
          fps: 10,
          qrbox: function (viewfinderWidth, viewfinderHeight) {
            // 保持正方形qrbox，使用较小边的60%作为扫码框大小
            const minEdge = Math.min(viewfinderWidth, viewfinderHeight)
            const qrboxSize = Math.floor(minEdge * 0.6)
            return {
              width: qrboxSize,
              height: qrboxSize,
            }
          },
          // 设置aspectRatio为屏幕比例，让摄像头画面铺满全屏
          aspectRatio: window.innerWidth / window.innerHeight,
        },
        (decodedText: string) => {
          console.log('扫码成功:', decodedText)

          // 停止扫码
          stopScanning()

          // 解析扫码结果获取device_id
          const deviceId = parseDeviceIdFromQrCode(decodedText)
          if (deviceId) {
            submitPrintTask(deviceId)
          } else {
            setStatus(PrintModalStatus.FAILED)
            setErrorMessage('无效的二维码')
          }
        },
        (errorMessage: string) => {
          // 扫码错误，通常可以忽略继续扫码
          console.log('扫码错误:', errorMessage)
        }
      )
    } catch (error: any) {
      console.error('启动扫码失败:', error)
      setStatus(PrintModalStatus.FAILED)
      setErrorMessage('无法启动摄像头，请检查权限设置')
      setIsScanning(false)
    }
  }

  // 停止扫码
  const stopScanning = async () => {
    if (scannerRef.current && isScanning) {
      try {
        await scannerRef.current.stop()
        scannerRef.current = null
        setIsScanning(false)
      } catch (error) {
        console.error('停止扫码失败:', error)
      }
    }
  }

  // 监听扫码状态变化，初始化扫码器
  useEffect(() => {
    if (status === PrintModalStatus.SCANNING) {
      // 延迟启动扫码，确保DOM元素已渲染
      const timer = setTimeout(() => {
        const qrReaderElement = document.getElementById('qr-reader')
        if (!qrReaderElement) {
          console.error('qr-reader元素未找到')
          setStatus(PrintModalStatus.FAILED)
          setErrorMessage('扫码器初始化失败')
          return
        }

        startScanning()
      }, 100)

      return () => {
        clearTimeout(timer)
      }
    }
  }, [status])

  // 确保扫码器全屏显示
  useEffect(() => {
    if (status === PrintModalStatus.SCANNING && isScanning) {
      const timer = setTimeout(() => {
        // 设置html5-qrcode生成的video元素样式
        const videoElement = document.querySelector('#qr-reader video')
        if (videoElement) {
          const video = videoElement as HTMLVideoElement
          video.style.width = '100vw'
          video.style.height = '100vh'
          video.style.objectFit = 'cover' // 保持比例并填满容器
          video.style.position = 'absolute'
          video.style.top = '0'
          video.style.left = '0'
        }

        // 设置扫码器容器样式
        const scannerElement = document.querySelector('#qr-reader')
        if (scannerElement) {
          const scanner = scannerElement as HTMLElement
          scanner.style.width = '100vw'
          scanner.style.height = '100vh'
          scanner.style.position = 'relative'
          scanner.style.overflow = 'hidden'
        }

        // 设置扫码框容器样式，确保qrbox正确显示
        const qrboxElement = document.querySelector('#qr-reader canvas')
        if (qrboxElement) {
          const canvas = qrboxElement as HTMLCanvasElement
          canvas.style.position = 'absolute'
          canvas.style.top = '50%'
          canvas.style.left = '50%'
          canvas.style.transform = 'translate(-50%, -50%)'
          canvas.style.zIndex = '5'
        }

        // 设置html5-qrcode的主容器样式
        const qrReaderDiv = document.querySelector('#qr-reader > div')
        if (qrReaderDiv) {
          const div = qrReaderDiv as HTMLElement
          div.style.width = '100vw'
          div.style.height = '100vh'
          div.style.position = 'relative'
        }

        // 确保所有子元素都能正确显示
        const allDivs = document.querySelectorAll('#qr-reader div')
        allDivs.forEach(div => {
          const element = div as HTMLElement
          if (element.style.width && element.style.height) {
            element.style.width = '100vw'
            element.style.height = '100vh'
          }
        })
      }, 500) // 等待扫码器完全初始化

      return () => {
        clearTimeout(timer)
      }
    }
  }, [status, isScanning])

  // 弹窗关闭时清理资源
  useEffect(() => {
    if (!open) {
      resetStatus()
    }
  }, [open])

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      resetStatus()
    }
  }, [])

  // 渲染不同状态的内容
  const renderContent = () => {
    switch (status) {
      case PrintModalStatus.INITIAL:
        return (
          <div className="p-2">
            <h1 className="text-[2.8rem] text-center font-semibold">
              {t('扫描二维码打印')}
            </h1>
            <p className="text-[2.1rem] leading-[2.8rem] text-center my-12">
              {t('请前往设备处扫码打印')}
            </p>
            <div className="flex justify-center">
              <Button
                className="mt-10 w-[20rem] border"
                size="lg"
                onClick={handleSubmit}
                ga-data="scanQrPrint"
              >
                {t('继续')}
              </Button>
            </div>
          </div>
        )

      case PrintModalStatus.SCANNING:
        return (
          <div className="fixed inset-0 z-50 bg-black">
            {/* 全屏扫码容器 */}
            <div
              id="qr-reader"
              className="w-full h-full"
              style={{
                width: '100vw',
                height: '100vh',
                position: 'relative',
              }}
            ></div>

            {/* 顶部标题覆盖层 */}
            <div className="absolute top-0 left-0 right-0 z-1 p-4">
              <h1 className="text-[2.4rem] text-center font-semibold text-white">
                {t('扫描二维码')}
              </h1>
            </div>

            {/* 底部提示覆盖层 */}
            <div className="absolute bottom-0 left-0 right-0 z-10 bg-black bg-opacity-50 p-4">
              <p className="text-[1.8rem] text-center text-white">
                {t('请将设备二维码对准扫描框')}
              </p>
              <div className="flex justify-center mt-4">
                <Button
                  className="bg-red-500 hover:bg-red-600 text-white"
                  onClick={() => {
                    stopScanning()
                    setStatus(PrintModalStatus.INITIAL)
                  }}
                >
                  {t('取消')}
                </Button>
              </div>
            </div>
          </div>
        )

      case PrintModalStatus.SUBMITTING:
        return (
          <div className="p-2 flex flex-col items-center">
            <MirrorLoading className="w-16 h-16 animate-spin mb-6" />
            <h1 className="text-[2.8rem] text-center font-semibold mb-4">
              {t('提交打印任务中')}
            </h1>
            <p className="text-[2.1rem] text-center text-gray-600">
              {t('请稍候...')}
            </p>
          </div>
        )

      case PrintModalStatus.PRINTING:
        return (
          <div className="p-2 flex flex-col items-center">
            <MirrorLoading className="w-16 h-16 animate-spin mb-6" />
            <h1 className="text-[2.8rem] text-center font-semibold mb-4">
              {t('正在打印')}
            </h1>
            <p className="text-[2.1rem] text-center text-gray-600">
              {t('请稍候，打印中...')}
            </p>
          </div>
        )

      case PrintModalStatus.SUCCESS:
        return (
          <div className="p-2 flex flex-col items-center">
            <img
              className="w-[12rem] mb-6"
              src={publicPreLoadSourceObj.paySuccess}
              alt="成功"
            />
            <h1 className="text-[2.8rem] text-center font-semibold mb-4">
              {t('打印成功')}
            </h1>
            <p className="text-[2.1rem] text-center text-gray-600">
              {t('请到设备处取照片')}
            </p>
          </div>
        )

      case PrintModalStatus.FAILED:
        return (
          <div className="p-2 flex flex-col items-center">
            <img
              className="w-[12rem] mb-6"
              src={publicPreLoadSourceObj.payFail}
              alt="失败"
            />
            <h1 className="text-[2.8rem] text-center font-semibold mb-4">
              {t('打印失败')}
            </h1>
            <p className="text-[2.1rem] text-center text-gray-600 mb-6">
              {errorMessage || t('请重试')}
            </p>
            <Button
              className="w-[20rem] border"
              size="lg"
              onClick={() => setStatus(PrintModalStatus.INITIAL)}
            >
              {t('重试')}
            </Button>
          </div>
        )

      default:
        return null
    }
  }

  // 扫码状态时直接渲染全屏组件
  if (open && status === PrintModalStatus.SCANNING) {
    return renderContent()
  }

  // 其他状态使用普通弹窗
  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 600 : '60vw'}
      content={renderContent()}
      showOkButton={false}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => {
        resetStatus()
        setOpen?.(false)
      }}
    />
  )
}
