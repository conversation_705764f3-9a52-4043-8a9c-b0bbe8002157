import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom } from '@/stores'
import { useAtomValue } from 'jotai'
import { Button } from '@/components/ui/shad/button'

export const ScanQrPrintModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  mazeImgUrl: string
  curMaterialId: number
}> = ({ open, setOpen, mazeImgUrl }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const handleSubmit = () => {}

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 600 : '60vw'}
      content={
        <div className="p-2">
          <h1 className="text-[2.8rem] text-center font-semibold">
            {t('扫描二维码打印')}
          </h1>
          <p className="text-[2.1rem] leading-[2.8rem] text-center my-12">
            {t('请前往设备处扫码打印')}
          </p>
          <div className="flex justify-center">
            <Button
              className="mt-10 w-[20rem] border"
              size="lg"
              onClick={handleSubmit}
              ga-data="scanQrPrint"
            >
              {t('继续')}
            </Button>
          </div>
        </div>
      }
      showOkButton={false}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
