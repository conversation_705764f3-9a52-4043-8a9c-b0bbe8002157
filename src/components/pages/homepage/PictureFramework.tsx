import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import {
  screenOrientationAtom,
  isTaskTypeSelectedAtom,
  isSupportVideoAtom,
  isShowThemeDetailModalAtom,
  selectedThemeDetailAtom,
  selectedGenderAtom,
  selectedEventDetail<PERSON>tom,
} from '@/stores'
// import { AutoScroll } from '@/components/ui/AutoScroll'
import { useEffect, useMemo, useState } from 'react'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import MultipleTemplateList from '@/components/pages/homepage/MultipleTemplateList'
import classNames from 'classnames'

import PrintQrModal from '@/components/business/PrintModal'
import { useTranslation } from 'react-i18next'
import { ThemeDetailModal } from './ThemeDetailModal'
import { ThemeDetail } from '@/apis/types'
import { isMachine, isPhone } from '@/utils'
import { AutoScroll } from '@/components/ui/AutoScroll'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/shad/button'

export const PictureFramework = () => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { t } = useTranslation()
  const navigate = useNavigate()

  // 选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | undefined | null
  >()
  // 选中的性别
  const [activeGender, setActiveGender] = useAtom(selectedGenderAtom)
  // 选中的 tag id
  const [activeTab, setActiveTab] = useState<number>(0)

  const [themeDetailModalOpen, setThemeDetailModalOpen] = useAtom(
    isShowThemeDetailModalAtom
  )

  const setSelectedThemeDetail = useSetAtom(selectedThemeDetailAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)

  useEffect(() => {
    if (activeTemplate) {
      setSelectedThemeDetail(activeTemplate)
    }
  }, [activeTemplate])

  const categoryList = useMemo(() => {
    return (
      selectedEventDetail?.tags?.map((it, i) => ({
        label: it.name as string,
        value: i as number,
        id: it.id as number,
      })) || []
    )
  }, [selectedEventDetail])

  // 使用所有分类下的模版并去重
  const selectTemplateList = useMemo(() => {
    const selectedThemes = selectedEventDetail?.tags?.[activeTab]?.themes || []
    return selectedThemes
  }, [selectedEventDetail, activeTab])

  // console.log(selectTemplateList, resourceTemplate)

  // 设置默认选中模板
  useEffect(() => {
    setActiveTemplate(selectTemplateList?.[0])
  }, [selectTemplateList])

  return (
    <>
      <PrintQrModal />
      <div className="relative w-full h-full">
        <div className="w-full absolute top-2 z-10 left-[50%] translate-x-[-50%]">
          <h1 className="maze-page-title">{t('选择您喜欢的风格')}</h1>
          {categoryList.length > 1 && (
            <div
              className={classNames(
                'relative mt-8 mx-auto ipad:mt-6',
                screenOrientation.isPortrait ? 'w-[80vw]' : 'w-[36vw]'
              )}
            >
              <AutoScroll
                activeIndex={categoryList.findIndex(
                  it => it.value === activeTab
                )}
                wrapClassName="p-1 rounded-full border-[2px] border-dashed bg-[#161616] iphone:border-[1px]"
                className="iphone:!py-2"
                listKey="categoryList"
              >
                <div className="flex">
                  {categoryList?.map(it => (
                    <div
                      className={classNames(
                        'text-[2rem] font-bold px-8 border-[3px] border-gray-200/0 py-3 rounded-full cursor-pointer whitespace-nowrap leading-none text-white iphone:border-[1px] iphone:px-6',
                        {
                          categoryItemActive: it.value === activeTab,
                        }
                      )}
                      key={it.value}
                      onClick={() => setActiveTab(it.value)}
                    >
                      {it.label}
                    </div>
                  ))}
                </div>
              </AutoScroll>
            </div>
          )}
        </div>
        {screenOrientation.isLandScape || isPhone() || isMachine() ? (
          // <SingleTemplateList
          <MazeSingleTemplateList
            activeGender={activeGender}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            selectTemplateList={selectTemplateList}
            listKey={`${activeGender}-${activeTab}`}
            multiline={false}
          />
        ) : (
          <MultipleTemplateList
            activeGender={activeGender}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            selectTemplateList={selectTemplateList}
            listKey={`${activeGender}-${activeTab}`}
            multiline={screenOrientation.isPortrait}
          />
        )}
        {/* Next 按钮 */}
        <div className="flex flex-col items-center absolute z-10 left-[50%] translate-x-[-50%] bottom-10">
          {activeTemplate ? (
            <Button
              size="lg"
              className={classNames(
                'flex maze-bg-gradient-btn w-[29.32rem] h-[9.375rem] text-[3.125rem] rounded-[150px] ',
                'ipad:w-[24rem] ipad:h-[8rem] ipad:text-[2.5rem]'
              )}
              variant="outline"
              onClick={() => {
                // 跳转到主题详情页面，传递主题ID
                navigate(`/theme?themeId=${activeTemplate.id}`)
              }}
            >
              {t('下一步')}
            </Button>
          ) : (
            <Button size="lg" className="flex opacity-[0.56]">
              {t('当前分类下暂无模板')}
            </Button>
          )}
        </div>
      </div>
      <ThemeDetailModal
        activeGender={activeGender}
        open={themeDetailModalOpen}
        setOpen={setThemeDetailModalOpen}
        themeDetail={activeTemplate}
      />
    </>
  )
}
