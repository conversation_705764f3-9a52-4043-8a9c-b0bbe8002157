/**
 * 机器打印弹窗
 */
import React, { useState, useEffect, useRef } from 'react'
import { MyModal } from '../ui/MyModal'
import { PortalContainer } from '../ui/PortalContainer'
import { isWebApp, isPhone } from '@/utils'
import { useTranslation } from 'react-i18next'
import { QRCodeCanvas } from 'qrcode.react'
import { useDevice } from '@/hooks/useDevice'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { PrintStatusEnum } from '@/apis/types'
import { PrintModal } from '@/components/pages/detail/PrintModal'
import { useAtomValue } from 'jotai'
import { selectedEventIdAtom } from '@/stores'
import { MirrorLoading } from 'wujieai-react-icon'

// 打印任务数据结构
interface PrintTask {
  material_id: number
  material_url: string
  status: number
  task_id: number
}

interface IProps {
  style?: React.CSSProperties
}

const PrintQrModal: React.FC<IProps> = ({ style }) => {
  const [open, setOpen] = useState(false)
  const [deviceId, setDeviceId] = useState('')
  const [printModalOpen, setPrintModalOpen] = useState(false)
  const [currentPrintTask, setCurrentPrintTask] = useState<PrintTask | null>(
    null
  )
  const { t } = useTranslation()
  const selectedEventId = useAtomValue(selectedEventIdAtom)
  const { getDefaultDeviceInfo } = useDevice()
  const timerRef = useRef<number | null>(null)

  const getDeviceId = async () => {
    try {
      const deviceInfo = await getDefaultDeviceInfo()
      setDeviceId(deviceInfo?.id || '')
    } catch (error) {
      console.error('获取设备信息失败:', error)
    }
  }
  useEffect(() => {
    getDeviceId()
  }, [])
  // 轮询打印任务
  const pollPrintTasks = async () => {
    try {
      const deviceInfo = await getDefaultDeviceInfo()
      if (!deviceInfo?.id) {
        console.log('设备ID不存在，无法轮询打印任务')
        return
      }

      const response = await _ajax.get(_api.print_task, {
        params: { device_id: deviceInfo.id },
      })

      if (response.data?.code === 200 && response.data?.data) {
        const tasks: PrintTask[] = response.data.data
        // 查找状态为PENDING的任务
        const pendingTask = tasks.find(
          task => task.status === PrintStatusEnum.PENDING
        )

        if (pendingTask) {
          console.log('发现待打印任务:', pendingTask)
          setCurrentPrintTask(pendingTask)
          setPrintModalOpen(true)
          // 停止轮询，等待打印完成
          stopPolling()
        }
      }
    } catch (error) {
      console.error('轮询打印任务失败:', error)
    }
  }

  // 开始轮询
  const startPolling = () => {
    if (timerRef.current) return // 防止重复启动

    // 立即执行一次
    pollPrintTasks()

    // 每5秒轮询一次
    timerRef.current = window.setInterval(pollPrintTasks, 3000)
  }

  // 停止轮询
  const stopPolling = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }

  // 上报打印状态
  const notifyPrintStatus = async (
    taskId: number,
    status: PrintStatusEnum,
    failReason?: string
  ) => {
    try {
      const deviceInfo = await getDefaultDeviceInfo()
      if (!deviceInfo?.id) return

      await _ajax.post(_api.print_notify, {
        event_id: selectedEventId, // 根据实际需求设置
        task_id: taskId,
        status: status,
        fail_reason: failReason || '',
      })

      console.log(`打印状态上报成功: task_id=${taskId}, status=${status}`)
    } catch (error) {
      console.error('上报打印状态失败:', error)
    }
  }

  // 打印开始回调
  const handlePrintStart = () => {
    if (currentPrintTask) {
      notifyPrintStatus(currentPrintTask.task_id, PrintStatusEnum.PRINTING)
    }
  }

  // 打印成功回调
  const handlePrintSuccess = () => {
    if (currentPrintTask) {
      notifyPrintStatus(currentPrintTask.task_id, PrintStatusEnum.SUCCESS)
      setCurrentPrintTask(null)
      setPrintModalOpen(false)
      // 重新开始轮询
      startPolling()
    }
  }

  // 打印失败回调
  const handlePrintError = (errorMessage?: string) => {
    if (currentPrintTask) {
      notifyPrintStatus(
        currentPrintTask.task_id,
        PrintStatusEnum.FAILED,
        errorMessage
      )
      setCurrentPrintTask(null)
      setPrintModalOpen(false)
      // 重新开始轮询
      startPolling()
    }
  }

  // 弹窗打开时开始轮询
  useEffect(() => {
    if (open) {
      startPolling()
    } else {
      stopPolling()
      // 关闭弹窗时也关闭打印弹窗
      setPrintModalOpen(false)
      setCurrentPrintTask(null)
    }

    return () => {
      stopPolling()
    }
  }, [open])

  if (isWebApp() || isPhone()) {
    return null
  }

  return (
    <>
      <PortalContainer>
        <div
          className={
            'h-[4rem] px-6 rounded-l-full bg-[rgba(0,0,0,0.32)] flex items-center justify-center fixed right-0 top-[72px] z-20 ' +
            'text-white text-[1.5rem] leading-[2rem] font-bold cursor-pointer'
          }
          ga-data="backHome"
          onClick={() => {
            setOpen(true)
          }}
          style={{
            ...style,
          }}
        >
          {t('打印')}
        </div>
      </PortalContainer>
      <MyModal
        open={open}
        content={
          <div className="flex-1 gap-10">
            {deviceId ? (
              <div className="w-auto mx-auto p-[32px] bg-white rounded-2xl">
                <QRCodeCanvas size={380} value={`${deviceId};`} />
              </div>
            ) : (
              <div className="w-auto mx-auto h-[380px] flex justify-center items-center p-[32px] bg-white/20 rounded-2xl">
                <MirrorLoading className="w-12 h-12 animate-spin opacity-50" />
              </div>
            )}
            <p className="pt-12 text-[2.2rem] text-center font-semibold">
              {t('扫描二维码打印')}
            </p>
          </div>
        }
        onCancel={() => {
          setOpen(false)
        }}
        showOkButton={false}
        width={580}
      />

      {/* 打印弹窗 */}
      {currentPrintTask && (
        <PrintModal
          open={printModalOpen}
          setOpen={open => {
            setPrintModalOpen(open)
            if (!open) {
              // 弹窗关闭时重新开始轮询
              setCurrentPrintTask(null)
              startPolling()
            }
          }}
          curImg={undefined} // 根据实际需求传入图片信息
          mazeImgUrl={currentPrintTask.material_url}
          onPrintStart={handlePrintStart}
          onPrintSuccess={handlePrintSuccess}
          onPrintError={handlePrintError}
        />
      )}
    </>
  )
}
export default PrintQrModal
