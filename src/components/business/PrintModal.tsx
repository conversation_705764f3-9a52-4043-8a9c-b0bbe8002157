/**
 * 机器打印弹窗
 */
import React, { useState } from 'react'
import { MyModal } from '../ui/MyModal'
import { PortalContainer } from '../ui/PortalContainer'
import { isWebApp, isPhone } from '@/utils'
import { useTranslation } from 'react-i18next'
import { QRCodeCanvas } from 'qrcode.react'
import { useDevice } from '@/hooks/useDevice'

interface IProps {
  style?: React.CSSProperties
}
const PrintQrModal: React.FC<IProps> = ({ style }) => {
  const [open, setOpen] = useState(false)
  const { t } = useTranslation()
  const { getDefaultDeviceInfo } = useDevice()

  if (isWebApp() || isPhone()) {
    return null
  }

  return (
    <>
      <PortalContainer>
        <div
          className={
            'h-[4rem] px-6 rounded-l-full bg-[rgba(0,0,0,0.32)] flex items-center justify-center fixed right-0 top-[72px] z-20 ' +
            'text-white text-[1.5rem] leading-[2rem] font-bold cursor-pointer'
          }
          ga-data="backHome"
          onClick={() => {
            setOpen(true)
          }}
          style={{
            ...style,
          }}
        >
          {t('打印')}
        </div>
      </PortalContainer>
      <MyModal
        open={open}
        content={
          <div className="flex-1 gap-10">
            <div className="w-auto mx-auto p-[32px] bg-white rounded-2xl">
              <QRCodeCanvas size={380} value="https://www.baidu.com" />
            </div>
            <p className="pt-12 text-[2.2rem] text-center font-semibold">
              {t('扫描二维码打印')}
            </p>
          </div>
        }
        onCancel={() => {
          setOpen(false)
        }}
        showOkButton={false}
        width={580}
      />
    </>
  )
}
export default PrintQrModal
